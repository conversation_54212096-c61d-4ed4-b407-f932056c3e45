# FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis and Caching
redis==5.0.1
aioredis==2.0.1

# Celery for async processing
celery==5.3.4
flower==2.0.1

# AI and ML
torch==2.1.1
torchvision==0.16.1
transformers==4.36.0
accelerate==0.25.0
bitsandbytes==0.41.3

# OCR Engines
paddlepaddle==2.5.2
paddleocr==2.7.3
pytesseract==0.3.10
easyocr==1.7.0

# Document Processing
PyMuPDF==1.23.8
Pillow==10.1.0
python-magic==0.4.27
python-multipart==0.0.6

# OpenAI and Anthropic
openai==1.3.7
anthropic==0.7.7

# HTTP and API
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# MinIO S3 Storage
minio==7.2.0
boto3==1.34.0

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Monitoring and Logging
prometheus-client==0.19.0
structlog==23.2.0
rich==13.7.0

# Utilities
python-dotenv==1.0.0
typer==0.9.0
click==8.1.7
tqdm==4.66.1
numpy==1.25.2
pandas==2.1.4

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
