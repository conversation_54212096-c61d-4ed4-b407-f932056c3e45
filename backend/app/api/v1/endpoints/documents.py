"""
Document upload and management endpoints
"""

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from typing import List, Optional
import uuid
import logging
from pathlib import Path
import aiofiles
import magic

from app.core.config import settings
from app.services.document_service import DocumentService
from app.workers.tasks import process_documents_task

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/upload")
async def upload_documents(
    files: List[UploadFile] = File(...),
    use_case: str = Form(default="uc05"),
    user_id: Optional[str] = Form(default=None)
):
    """
    Upload documents for processing
    
    Args:
        files: List of uploaded files
        use_case: Use case ID (uc01-uc18)
        user_id: Optional user identifier
        
    Returns:
        Job ID and upload details
    """
    try:
        # Validate use case
        if use_case not in settings.SUPPORTED_USE_CASES:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported use case: {use_case}"
            )
        
        # Validate files
        if not files:
            raise HTTPException(
                status_code=400,
                detail="No files provided"
            )
        
        # Generate job ID
        job_id = str(uuid.uuid4())
        upload_dir = Path(settings.DATA_DIR) / "uploads" / job_id
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        uploaded_files = []
        
        for file in files:
            # Validate file size
            if file.size > settings.MAX_FILE_SIZE:
                raise HTTPException(
                    status_code=400,
                    detail=f"File {file.filename} exceeds maximum size of {settings.MAX_FILE_SIZE} bytes"
                )
            
            # Read file content
            content = await file.read()
            
            # Validate file type using python-magic
            mime_type = magic.from_buffer(content, mime=True)
            if mime_type not in settings.ALLOWED_FILE_TYPES:
                logger.warning(f"File {file.filename} has unsupported MIME type: {mime_type}")
                # Continue processing but log warning
            
            # Save file
            file_path = upload_dir / file.filename
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)
            
            uploaded_files.append({
                "filename": file.filename,
                "size": file.size,
                "mime_type": mime_type,
                "path": str(file_path)
            })
            
            logger.info(f"Uploaded file: {file.filename} ({file.size} bytes)")
        
        # Create processing job
        job_data = {
            "job_id": job_id,
            "use_case": use_case,
            "user_id": user_id,
            "files": uploaded_files,
            "status": "uploaded",
            "created_at": "now"
        }
        
        # Start async processing
        task = process_documents_task.delay(job_id, use_case, uploaded_files)
        
        logger.info(f"Started processing job {job_id} with {len(uploaded_files)} files")
        
        return {
            "job_id": job_id,
            "task_id": task.id,
            "use_case": use_case,
            "files_uploaded": len(uploaded_files),
            "total_size": sum(f["size"] for f in uploaded_files),
            "status": "processing",
            "message": "Files uploaded successfully and processing started"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Upload failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Upload failed: {str(e)}"
        )

@router.get("/{job_id}/status")
async def get_upload_status(job_id: str):
    """Get upload and processing status"""
    try:
        # Check if job exists
        upload_dir = Path(settings.DATA_DIR) / "uploads" / job_id
        if not upload_dir.exists():
            raise HTTPException(
                status_code=404,
                detail="Job not found"
            )
        
        # Get processing status from Celery
        from app.workers.celery import celery_app
        
        # Find task by job_id (simplified - in production use proper task tracking)
        task_id = f"process_documents_{job_id}"
        
        return {
            "job_id": job_id,
            "status": "processing",  # Simplified for demo
            "progress": 50,
            "message": "Processing documents..."
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Status check failed: {str(e)}"
        )

@router.get("/{job_id}/files")
async def list_uploaded_files(job_id: str):
    """List files for a job"""
    try:
        upload_dir = Path(settings.DATA_DIR) / "uploads" / job_id
        if not upload_dir.exists():
            raise HTTPException(
                status_code=404,
                detail="Job not found"
            )
        
        files = []
        for file_path in upload_dir.iterdir():
            if file_path.is_file():
                stat = file_path.stat()
                files.append({
                    "filename": file_path.name,
                    "size": stat.st_size,
                    "modified": stat.st_mtime,
                    "path": str(file_path)
                })
        
        return {
            "job_id": job_id,
            "files": files,
            "total_files": len(files)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File listing failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"File listing failed: {str(e)}"
        )

@router.delete("/{job_id}")
async def delete_job(job_id: str):
    """Delete a job and all associated files"""
    try:
        upload_dir = Path(settings.DATA_DIR) / "uploads" / job_id
        if not upload_dir.exists():
            raise HTTPException(
                status_code=404,
                detail="Job not found"
            )
        
        # Delete all files in the job directory
        import shutil
        shutil.rmtree(upload_dir)
        
        # Also delete processed results if they exist
        results_dir = Path(settings.DATA_DIR) / "processed" / job_id
        if results_dir.exists():
            shutil.rmtree(results_dir)
        
        logger.info(f"Deleted job {job_id}")
        
        return {
            "job_id": job_id,
            "status": "deleted",
            "message": "Job and all associated files deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Job deletion failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Job deletion failed: {str(e)}"
        )
