"""
API Router for Zurich Challenge endpoints
"""

from fastapi import APIRouter
from app.api.v1.endpoints import (
    documents,
    processing,
    results,
    use_cases,
    health
)

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(
    health.router,
    tags=["health"]
)

api_router.include_router(
    documents.router,
    prefix="/documents",
    tags=["documents"]
)

api_router.include_router(
    processing.router,
    prefix="/processing",
    tags=["processing"]
)

api_router.include_router(
    results.router,
    prefix="/results",
    tags=["results"]
)

api_router.include_router(
    use_cases.router,
    prefix="/use-cases",
    tags=["use-cases"]
)
