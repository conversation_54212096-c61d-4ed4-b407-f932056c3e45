"""
Celery tasks for async document processing
"""

import logging
import json
from pathlib import Path
from typing import List, Dict, Any
from celery import current_task

from app.workers.celery import celery_app
from app.core.config import settings
from app.services.ocr_service import OCRService
from app.services.ai_service import AIService
from app.use_cases.uc05_liability.liability_analyzer import LiabilityAnalyzer

logger = logging.getLogger(__name__)

@celery_app.task(bind=True)
def process_documents_task(self, job_id: str, use_case: str, uploaded_files: List[Dict]):
    """
    Main task for processing uploaded documents
    
    Args:
        job_id: Unique job identifier
        use_case: Use case ID (uc01-uc18)
        uploaded_files: List of uploaded file information
    """
    try:
        logger.info(f"🚀 Starting document processing for job {job_id}")
        
        # Update task status
        self.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': len(uploaded_files), 'status': 'Starting...'}
        )
        
        # Initialize services
        ocr_service = OCRService()
        ai_service = AIService()
        
        # Process each document
        processed_documents = []
        
        for i, file_info in enumerate(uploaded_files):
            try:
                logger.info(f"📄 Processing file {i+1}/{len(uploaded_files)}: {file_info['filename']}")
                
                # Update progress
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'current': i,
                        'total': len(uploaded_files),
                        'status': f'Processing {file_info["filename"]}...'
                    }
                )
                
                # Step 1: OCR extraction
                ocr_result = await ocr_service.extract_text(
                    file_info['path'],
                    engine='auto'
                )
                
                # Step 2: Document classification and processing
                document_data = {
                    'filename': file_info['filename'],
                    'text': ocr_result['text'],
                    'ocr_confidence': ocr_result['confidence'],
                    'ocr_engine': ocr_result['engine'],
                    'file_size': file_info['size'],
                    'mime_type': file_info['mime_type']
                }
                
                processed_documents.append(document_data)
                
                logger.info(f"✅ Processed {file_info['filename']} - OCR confidence: {ocr_result['confidence']:.2f}")
                
            except Exception as e:
                logger.error(f"❌ Failed to process {file_info['filename']}: {e}")
                # Continue with other files
                processed_documents.append({
                    'filename': file_info['filename'],
                    'text': '',
                    'ocr_confidence': 0.0,
                    'error': str(e)
                })
        
        # Update progress - OCR complete
        self.update_state(
            state='PROGRESS',
            meta={
                'current': len(uploaded_files),
                'total': len(uploaded_files) + 1,
                'status': 'Running AI analysis...'
            }
        )
        
        # Step 3: Use case specific analysis
        analysis_result = await run_use_case_analysis(use_case, processed_documents)
        
        # Step 4: Save results
        results_dir = Path(settings.DATA_DIR) / "processed" / job_id
        results_dir.mkdir(parents=True, exist_ok=True)
        
        final_result = {
            'job_id': job_id,
            'use_case': use_case,
            'documents': processed_documents,
            'analysis': analysis_result,
            'status': 'completed',
            'total_documents': len(uploaded_files),
            'successful_documents': len([d for d in processed_documents if 'error' not in d])
        }
        
        # Save to JSON file
        result_file = results_dir / "results.json"
        with open(result_file, 'w') as f:
            json.dump(final_result, f, indent=2, default=str)
        
        logger.info(f"🎉 Job {job_id} completed successfully")
        
        return final_result
        
    except Exception as e:
        logger.error(f"❌ Job {job_id} failed: {e}")
        self.update_state(
            state='FAILURE',
            meta={'error': str(e), 'status': 'Failed'}
        )
        raise

async def run_use_case_analysis(use_case: str, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Run use case specific analysis
    
    Args:
        use_case: Use case identifier
        documents: Processed documents with OCR text
        
    Returns:
        Analysis results specific to the use case
    """
    try:
        if use_case == "uc05":
            # Liability Decisions
            analyzer = LiabilityAnalyzer()
            assessment = await analyzer.analyze_liability(documents)
            
            return {
                'use_case': 'uc05',
                'type': 'liability_assessment',
                'insured_fault_percentage': assessment.insured_fault_percentage,
                'third_party_fault_percentage': assessment.third_party_fault_percentage,
                'confidence_score': assessment.confidence_score,
                'supporting_evidence': [
                    {
                        'type': ev.type,
                        'content': ev.content,
                        'confidence': ev.confidence,
                        'source': ev.source_document,
                        'relevance': ev.relevance_score
                    }
                    for ev in assessment.supporting_evidence
                ],
                'risk_factors': assessment.risk_factors,
                'recommendation': assessment.recommendation,
                'reasoning': assessment.reasoning
            }
            
        elif use_case == "uc01":
            # Travel Claims (simplified implementation)
            return await analyze_travel_claims(documents)
            
        else:
            # Generic analysis for other use cases
            return await analyze_generic_use_case(use_case, documents)
            
    except Exception as e:
        logger.error(f"❌ Use case analysis failed for {use_case}: {e}")
        return {
            'use_case': use_case,
            'type': 'error',
            'error': str(e),
            'status': 'analysis_failed'
        }

async def analyze_travel_claims(documents: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze travel claims (UC01)"""
    # Simplified implementation for demo
    total_amount = 0
    receipts_found = 0
    
    for doc in documents:
        text = doc.get('text', '').lower()
        
        # Look for receipt indicators
        if any(keyword in text for keyword in ['receipt', 'invoice', 'bill', 'payment']):
            receipts_found += 1
        
        # Extract amounts (simplified)
        import re
        amounts = re.findall(r'\$[\d,]+(?:\.\d{2})?', text)
        for amount in amounts:
            try:
                value = float(amount.replace('$', '').replace(',', ''))
                total_amount += value
            except:
                continue
    
    return {
        'use_case': 'uc01',
        'type': 'travel_claim_analysis',
        'total_claimed_amount': total_amount,
        'receipts_found': receipts_found,
        'documents_processed': len(documents),
        'recommendation': 'approve' if receipts_found >= len(documents) * 0.8 else 'review',
        'confidence_score': min(receipts_found / len(documents), 1.0) if documents else 0
    }

async def analyze_generic_use_case(use_case: str, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Generic analysis for other use cases"""
    # Extract basic statistics
    total_text_length = sum(len(doc.get('text', '')) for doc in documents)
    avg_confidence = sum(doc.get('ocr_confidence', 0) for doc in documents) / len(documents) if documents else 0
    
    return {
        'use_case': use_case,
        'type': 'generic_analysis',
        'documents_processed': len(documents),
        'total_text_extracted': total_text_length,
        'average_ocr_confidence': avg_confidence,
        'status': 'completed',
        'message': f'Processed {len(documents)} documents for {use_case}'
    }

@celery_app.task
def cleanup_old_jobs():
    """Cleanup old job files (run periodically)"""
    try:
        import time
        import shutil
        
        current_time = time.time()
        cleanup_age = 7 * 24 * 60 * 60  # 7 days
        
        uploads_dir = Path(settings.DATA_DIR) / "uploads"
        processed_dir = Path(settings.DATA_DIR) / "processed"
        
        cleaned_count = 0
        
        for directory in [uploads_dir, processed_dir]:
            if directory.exists():
                for job_dir in directory.iterdir():
                    if job_dir.is_dir():
                        # Check age
                        if current_time - job_dir.stat().st_mtime > cleanup_age:
                            shutil.rmtree(job_dir)
                            cleaned_count += 1
                            logger.info(f"Cleaned up old job: {job_dir.name}")
        
        logger.info(f"Cleanup completed: {cleaned_count} old jobs removed")
        return {"cleaned_jobs": cleaned_count}
        
    except Exception as e:
        logger.error(f"Cleanup failed: {e}")
        raise
