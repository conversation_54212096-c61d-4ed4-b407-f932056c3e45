import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import Dashboard from './pages/Dashboard';
import ProcessingPage from './pages/ProcessingPage';
import ResultsPage from './pages/ResultsPage';
import { useApi } from './hooks/useApi';
import './App.css';

function App() {
  const [selectedUseCase, setSelectedUseCase] = useState('uc05');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [systemStatus, setSystemStatus] = useState('loading');
  const { get } = useApi();

  // Check system health on startup
  useEffect(() => {
    const checkHealth = async () => {
      try {
        await get('/health');
        setSystemStatus('healthy');
      } catch (error) {
        console.error('Health check failed:', error);
        setSystemStatus('error');
      }
    };

    checkHealth();
    
    // Check health every 30 seconds
    const interval = setInterval(checkHealth, 30000);
    return () => clearInterval(interval);
  }, [get]);

  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        {/* Toast notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              iconTheme: {
                primary: '#10B981',
                secondary: '#fff',
              },
            },
            error: {
              duration: 5000,
              iconTheme: {
                primary: '#EF4444',
                secondary: '#fff',
              },
            },
          }}
        />

        {/* Header */}
        <Header
          selectedUseCase={selectedUseCase}
          onUseCaseChange={setSelectedUseCase}
          systemStatus={systemStatus}
          onMenuClick={() => setSidebarOpen(!sidebarOpen)}
        />

        <div className="flex">
          {/* Sidebar */}
          <Sidebar
            isOpen={sidebarOpen}
            onClose={() => setSidebarOpen(false)}
            selectedUseCase={selectedUseCase}
            onUseCaseChange={setSelectedUseCase}
          />

          {/* Main content */}
          <main className={`flex-1 transition-all duration-300 ${
            sidebarOpen ? 'ml-64' : 'ml-0'
          }`}>
            <div className="p-6">
              <Routes>
                <Route 
                  path="/" 
                  element={
                    <Dashboard 
                      selectedUseCase={selectedUseCase}
                      systemStatus={systemStatus}
                    />
                  } 
                />
                <Route 
                  path="/processing/:jobId" 
                  element={
                    <ProcessingPage 
                      selectedUseCase={selectedUseCase}
                    />
                  } 
                />
                <Route 
                  path="/results/:resultId" 
                  element={
                    <ResultsPage 
                      selectedUseCase={selectedUseCase}
                    />
                  } 
                />
              </Routes>
            </div>
          </main>
        </div>

        {/* System status indicator */}
        {systemStatus === 'error' && (
          <div className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg">
            ⚠️ System connection error
          </div>
        )}
      </div>
    </Router>
  );
}

export default App;
